apiVersion: apps/v1
kind: Deployment
metadata:
  name: assistant-service
  namespace: abraapi
  labels:
    app: assistant-service
    service: assistant
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: assistant-service
  template:
    metadata:
      labels:
        app: assistant-service
        service: assistant
        version: v1
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: assistant-service
        image: ghcr.io/arnyfesto1/abraapp-assistant-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_URLS
        - name: OLLAMA_URL
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: OLLAMA_URL
        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: SUPABASE_CONNECTION_STRING
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
        - name: ghcr-creds