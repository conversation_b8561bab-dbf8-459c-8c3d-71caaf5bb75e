apiVersion: apps/v1
kind: Deployment
metadata:
  name: thread-service
  namespace: abraapi
  labels:
    app: thread-service
    service: thread
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: thread-service
  template:
    metadata:
      labels:
        app: thread-service
        service: thread
        version: v1
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: thread-service
        image: ghcr.io/arnyfesto1/abraapp-thread-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: thread-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: thread-service-config
              key: ASPNETCORE_URLS
        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_CONNECTION_STRING
        - name: S<PERSON><PERSON><PERSON>E_URL
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_JWT_SECRET
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
        - name: ghcr-creds