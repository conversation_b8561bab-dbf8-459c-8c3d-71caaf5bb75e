apiVersion: apps/v1
kind: Deployment
metadata:
  name: marketdata-service
  namespace: abraapi
  labels:
    app: marketdata-service
    service: marketdata
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: marketdata-service
  template:
    metadata:
      labels:
        app: marketdata-service
        service: marketdata
        version: v1
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: marketdata-service
        image: ghcr.io/arnyfesto1/abraapp-marketdata-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: marketdata-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: marketdata-service-config
              key: ASPNETCORE_URLS
        - name: Redis__ConnectionString
          valueFrom:
            configMapKeyRef:
              name: marketdata-service-config
              key: Redis__ConnectionString
        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: SUPABASE_CONNECTION_STRING
        - name: FINNHUB_API_KEY
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: FINNHUB_API_KEY
        - name: POLYGON_API_KEY
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: POLYGON_API_KEY
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: SUPABASE_JWT_SECRET
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
        - name: ghcr-creds